import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UsageService } from '../usage.service';
import { UsageType } from '@revalu/database';

export const USAGE_LIMIT_KEY = 'usageLimit';

export interface UsageLimitOptions {
  type: UsageType;
  count?: number;
  autoRecord?: boolean;
}

export const UsageLimit = (options: UsageLimitOptions) => {
  return (target: any, propertyKey?: string, descriptor?: PropertyDescriptor) => {
    const reflector = new Reflector();
    reflector.set(USAGE_LIMIT_KEY, options, descriptor?.value || target);
  };
};

@Injectable()
export class UsageLimitGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private usageService: UsageService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const usageLimitOptions = this.reflector.getAllAndOverride<UsageLimitOptions>(
      USAGE_LIMIT_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!usageLimitOptions) {
      return true; // No usage limit specified
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      return true; // No user, let auth guard handle it
    }

    const { type, count = 1, autoRecord = true } = usageLimitOptions;

    // Check if user can perform this action
    const canPerform = await this.usageService.canPerformAction(user.id, type, count);

    if (!canPerform) {
      const errorMessage = this.getUsageLimitErrorMessage(type);
      throw new ForbiddenException(errorMessage);
    }

    // Auto-record usage if enabled
    if (autoRecord) {
      // Store usage info in request for later recording
      request.usageToRecord = { type, count };
    }

    return true;
  }

  private getUsageLimitErrorMessage(usageType: UsageType): string {
    switch (usageType) {
      case UsageType.SEARCH:
        return 'You have reached your search limit. Please upgrade your plan to continue searching.';
      case UsageType.PROPERTY_TRACK:
        return 'You have reached your property tracking limit. Please upgrade your plan to track more properties.';
      case UsageType.REPORT_GENERATION:
        return 'You have reached your report generation limit. Please upgrade your plan to generate more reports.';
      case UsageType.EXPORT:
        return 'Export feature is not available in your current plan. Please upgrade to access this feature.';
      default:
        return 'You have reached your usage limit for this feature. Please upgrade your plan.';
    }
  }
}
