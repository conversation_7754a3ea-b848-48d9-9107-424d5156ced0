import { Module } from '@nestjs/common';
import { SubscriptionsController } from './subscriptions.controller';
import { SubscriptionsService } from './subscriptions.service';
import { UsageService } from './usage.service';
import { PlanService } from './plan.service';

@Module({
  controllers: [SubscriptionsController],
  providers: [SubscriptionsService, UsageService, PlanService],
  exports: [SubscriptionsService, UsageService, PlanService],
})
export class SubscriptionsModule {}
